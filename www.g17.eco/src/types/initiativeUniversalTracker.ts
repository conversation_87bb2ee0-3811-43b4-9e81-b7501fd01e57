import { UtrvConfigType } from '../features/question-configuration/types';
import { BaseAggregationConfig, UniversalTrackerPlain , UtrAggregationConfig, ValueAggregation } from './universalTracker';

export type OverrideAggregationConfig = BaseAggregationConfig<ValueAggregation | 'default'>;

export interface InitiativeUniversalTracker
  extends Pick<UniversalTrackerPlain, 'unitInput' | 'numberScaleInput' | 'valueValidation' | 'unitLocked' | 'numberScaleLocked'> {
  _id: string;
  initiativeId: string;
  universalTrackerId: string;
  utrvConfig?: UtrvConfigType;
  aggregationConfig?: UtrAggregationConfig;
}
