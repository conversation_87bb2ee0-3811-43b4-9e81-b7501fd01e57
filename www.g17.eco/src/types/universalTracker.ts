/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { SerializedEditorState } from 'lexical';
import { Option, ValueListPlain } from '@g17eco/types/valueList';

export enum UniversalTrackerType {
  Utr = 'utr',
}

export type UniversalTrackerBase = Pick<
  UniversalTrackerPlain,
  | '_id'
  | 'code'
  | 'typeCode'
  | 'name'
  | 'description'
  | 'alternatives'
  | 'type'
  | 'valueLabel'
  | 'numberScale'
  | 'tags'
  | 'unit'
  | 'instructions'
  | 'instructionsEditorState'
  | 'unitType'
  | 'valueValidation'
  | 'typeTags'
  | 'targetDirection'
  | 'numberScaleInput'
  | 'instructionsLink'
  | 'evidenceInstructions'
  | 'valueType'
> & {
  created?: string;
};

export type AlternativeProps = Pick<
  UniversalTrackerBase,
  'name' | 'description' | 'valueLabel' | 'instructions' | 'evidenceInstructions' | 'instructionsLink' | 'typeCode'
>;

export enum UtrValueType {
  Number = 'number',
  Sample = 'sample',
  Percentage = 'percentage',
  Text = 'text',
  Date = 'date',
  ValueListMulti = 'valueListMulti', // MultiSelect
  ValueList = 'valueList', // Single Select
  NumericValueList = 'numericValueList', // Numeric Multi Checkbox
  TextValueList = 'textValueList', // Multi Checkbox
  Table = 'table',
}

export enum TableColumnType {
  Number = 'number',
  Text = 'text',
  Date = 'date',
  ValueList = 'valueList',
  ValueListMulti = 'valueListMulti',
  Percentage = 'percentage',
}

export const hasMinAndMaxColumnType = (
  columnType: TableColumnType,
  min: number | undefined,
  max: number | undefined,
) => {
  switch (columnType) {
    case TableColumnType.Number:
      return min === 0 && max === 100;
    case TableColumnType.Percentage:
      return min !== undefined && max !== undefined && min < max;
    default:
      return false;
  }
};

export const getTableColumnTypeNames = (type: TableColumnType, hasListId: boolean = false, col?: TableColumn) => {
  const names = {
    [TableColumnType.Number]: 'Number',
    [TableColumnType.Text]: 'Text',
    [TableColumnType.Date]: 'Date',
    [TableColumnType.Percentage]: 'Number',
    [TableColumnType.ValueListMulti]: 'Text Multiple-Choices',
    [TableColumnType.ValueList]: 'Text Multiple-Choice',
  };
  if (type === TableColumnType.Text && hasListId) {
    return names[TableColumnType.ValueList];
  }
  if (hasMinAndMaxColumnType(type, col?.validation?.min, col?.validation?.max)) {
    return 'Percentage';
  }
  return names[type];
};

export interface UniversalTrackerMin {
  _id: string;
  name: string;
  type: string;
  valueType?: string;
}

export interface Tags {
  cdsb?: string[];
  tcfd?: string[];
  pic?: string[];
  ese?: string[];
  sdg?: string[];
  wef?: string[];
  ungc?: string[];
  sec?: string[];
  see?: string[];
  /** @deprecated replaced with eesg_2024 */
  esg30?: string[];
  eesg_2024?: string[];
}

export interface UniversalTrackerBlueprintMin extends UniversalTrackerMin {
  _id: string;
  code: string;
  typeCode: string;
  valueLabel: string;
  alternatives?: { [key: string]: Alternative | LanguageAlternative };
  tags?: Tags;
  valueValidation?: ValueValidation;
  instructions?: string;
  valueType: string;
  typeTags?: string[];
  unit?: string;
  unitType?: string;
  numberScale?: string;
  ownerId?: string;
}

export type BulkActionUtrMin = Partial<
  Pick<UniversalTrackerBlueprintMin, 'name' | 'valueValidation' | 'unit' | 'unitType' | 'valueType' | 'numberScale'>
>;

export interface Alternative {
  name: string;
  valueLabel: string;
  alternativeType?: string;
  languageCode?: string;
  typeCode?: string;
  description?: string;
  instructions?: string;
  instructionsEditorState?: SerializedEditorState;
  instructionsLink?: string;
  evidenceInstructions?: string;
  sampleSizeLabel?: string;
  typeTags?: string[];
}

export interface LanguageAlternative extends Alternative {
  alternativeType: 'language';
  languageCode: string;
}

export enum VariationDataSource {
  LastMonth = 'last_month',
  LastYear = 'last_year',
  LatestVerified = 'latest_verified',
}

export enum VariationType {
  Percentage = 'percentage',
}

export interface Variation {
  type: VariationType;
  min: number;
  max: number;
  dataSource: VariationDataSource;
  confirmationRequired: boolean;
}

export interface ValueValidation {
  min?: number;
  max?: number;
  decimal?: number;
  variations?: Variation[];
  valueList?: {
    list?: Option[];
    type: 'list' | 'custom';
    listId?: string;
    custom: any;
    allowCustomOptions?: boolean;
  };
  table?: ValueTable;
}

interface Calculation {
  formula: string;
  useDisaggregations?: boolean;
  variables: {
    [key: string]: CalculationVariable;
  };
}

export interface Connection extends Calculation {
  score: number;
}

export enum ConditionVisibility {
  Disabled = 'disabled',
  Hidden = 'hidden',
}

interface Condition {
  code: string;
  calculation: Calculation;
  result: {
    visibility: ConditionVisibility;
    userMessage?: string;
  };
}

export interface UniversalTrackerPlain extends UniversalTrackerMin {
  _id: string;
  instructions?: string;
  instructionsEditorState?: SerializedEditorState;
  code: string;
  created: string;
  valueListOrdered?: any[];
  valueListTargets?: any[];
  description?: string;
  type: string;
  typeCode: string;
  typeTags?: string[];
  targetDirection?: string;
  valueLabel: string;
  valueType: string;
  name: string;
  ownerId?: string;
  unitType?: string;
  unit?: string;
  unitInput?: string;
  numberScaleInput?: string;
  unitLocked?: boolean;
  numberScaleLocked?: boolean;
  numberScale?: string;
  valueValidation?: ValueValidation;
  alternatives?: { [key: string]: Alternative | LanguageAlternative };
  tags?: Tags;
  valueListOptions?: ValueListPlain;
  tableColumnValueListOptions?: ValueListPlain[];
  calculation?: Calculation;
  conditions?: Condition[];
  connections?: Connection[];
  instructionsLink?: string;
  evidenceInstructions?: string;
}

export interface CalculationVariable {
  code: string;
  valueAggregation?: string | null;
  valueListCode?: string;
}

export interface UniveresalTrackerGroup {
  _id: string;
  code: string;
  name: string;
  initiativeId: string;
  universalTrackers: string[];
}

export type ConnectionUtr = Pick<
  UniversalTrackerPlain,
  | '_id'
  | 'code'
  | 'name'
  | 'valueLabel'
  | 'type'
  | 'valueType'
  | 'valueValidation'
  | 'typeCode'
  | 'unit'
  | 'unitType'
  | 'numberScale'
>;

export interface EvidenceLink {
  url: string;
  description?: string;
}

export enum UtrType {
  Calculation = 'calculation',
  Kpi = 'kpi',
  CustomKpi = 'custom_kpi',
  SdgComponent = 'sdg-component',
  Generated = 'generated',
}

/**
 * Allow to group using list of columns
 */
export interface TableGroupColumn {
  /** Column code to group by **/
  code: string;
}

export enum TableAggregationType {
  Group = 'group',
}

interface TableAggregation {
  type: TableAggregationType.Group;
  columns: TableGroupColumn[];
}

export interface ValueTable {
  validation?: {
    maxRows: number;
  };
  columns: TableColumn[];
  aggregation?: TableAggregation;
}

export interface TableColumn {
  type: TableColumnType;
  code: string;
  name: string;
  shortName?: string;
  options?: Option[];
  unit?: string;
  listId?: string;
  unitType?: string;
  numberScale?: string;
  unitInput?: string;
  numberScaleInput?: string;
  unitLocked?: boolean;
  numberScaleLocked?: boolean;
  instructions?: string;
  calculation?: {
    formula: string;
  };
  visibilityRule?: {
    formula: string;
  };
  validation?: {
    max?: number;
    min?: number;
    decimal?: number;
    variations?: Variation[];
    required?: boolean;
    allowCustomOptions?: boolean;
  };
}

// Move to universalTracker to remove circular dependency
export enum ValueAggregation {
  ValueSumAggregator = 'valueSumAggregator',
  ValueCountAggregator = 'valueCountAggregator', // Just count utrvs
  ValueConcatenateAggregator = 'valueConcatenateAggregator',
  ValueAverageAggregator = 'valueAverageAggregator',
  ValueListCountAggregator = 'valueListCountAggregator',
  TextCountAggregator = 'textCountAggregator',
  NumericValueListSumAggregator = 'numericValueListSumAggregator',
  NumericValueListAverageAggregator = 'numericValueListAverageAggregator',
  TableColumnAggregator = 'tableAggregator',
  TableConcatenationAggregator = 'tableConcatenationAggregator',
  LatestAggregator = 'latestAggregator',
  EmptyAggregator = 'emptyAggregator',
  TableRowGroupAggregator = 'tableRowGroupAggregator',
}

export enum ColumnValueAggregation {
  ColumnSumAggregator = 'columnSumAggregator',
  ColumnAverageAggregator = 'columnAverageAggregator',
  ColumnLatestAggregator = 'columnLatestAggregator',
  ColumnMaxAggregator = 'columnMaxAggregator',
  ColumnEmptyAggregator = 'columnEmptyAggregator',
  ColumnConcatenateAggregator = 'columnConcatenateAggregator',
  ColumnPostAggregationCalculation = 'columnPostAggregationCalculation',
  ColumnWeightedAverageAggregator = 'columnWeightedAverageAggregator',
}

// Aggregation mode enum for better organization
export enum AggregationMode {
  Children = 'children', // Used for tree aggregations up to parent
  Combined = 'combined', // Used for combined reports (e.g. monthly reports to a combined yearly)
}

/**
 * Core interface for aggregation mode configuration
 * Contains common fields used across all aggregation contexts
 */
export interface AggregationModeConfig<T extends ColumnValueAggregation | ValueAggregation | string> {
  valueAggregation: T;
  weightFormula?: string;
}


/**
 * Base aggregation configuration interface
 * Uses enum keys for better type safety
 */
export interface BaseAggregationConfig<T extends ColumnValueAggregation | ValueAggregation | string> {
  modes?: {
    children?: AggregationModeConfig<T>;
    combined?: AggregationModeConfig<T>;
  };
}

/**
 * UTR-specific aggregation configuration
 * Constrains valueAggregation to UTR-level aggregations only
 */
export type UtrAggregationConfig = BaseAggregationConfig<ValueAggregation>;

/**
 * Column-specific aggregation configuration
 * Constrains valueAggregation to column-level aggregations only
 */
export type ColumnAggregationConfig = BaseAggregationConfig<ColumnValueAggregation>;

type ValueAggregationCompatibilityInterface = {
  [key in UtrValueType]: {
    default: ValueAggregation;
    compatible: ValueAggregation[];
  };
};

type ColumnAggregationCompatibilityInterface = {
  [key in TableColumnType]: {
    default: ColumnValueAggregation;
    compatible: ColumnValueAggregation[];
  };
};

const defaultCompatible = [
  ValueAggregation.LatestAggregator,
  ValueAggregation.EmptyAggregator,
  ValueAggregation.ValueCountAggregator,
];

const defaultLatestCompatibility = {
  default: ValueAggregation.LatestAggregator,
  compatible: defaultCompatible,
};

export const ValueAggregationSiblingsCompatibility: ValueAggregationCompatibilityInterface = {
  [UtrValueType.Number]: {
    default: ValueAggregation.ValueSumAggregator,
    compatible: [...defaultCompatible, ValueAggregation.ValueSumAggregator, ValueAggregation.ValueAverageAggregator],
  },
  [UtrValueType.Percentage]: {
    default: ValueAggregation.ValueAverageAggregator,
    compatible: [...defaultCompatible, ValueAggregation.ValueSumAggregator, ValueAggregation.ValueAverageAggregator],
  },
  [UtrValueType.Table]: {
    default: ValueAggregation.TableColumnAggregator,
    compatible: [
      ...defaultCompatible,
      ValueAggregation.TableColumnAggregator,
      ValueAggregation.TableConcatenationAggregator,
      ValueAggregation.TableRowGroupAggregator,
    ],
  },
  [UtrValueType.NumericValueList]: {
    default: ValueAggregation.NumericValueListSumAggregator,
    compatible: [
      ...defaultCompatible,
      ValueAggregation.NumericValueListSumAggregator,
      ValueAggregation.NumericValueListAverageAggregator,
    ],
  },
  [UtrValueType.ValueListMulti]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [...defaultCompatible, ValueAggregation.ValueListCountAggregator],
  },
  [UtrValueType.ValueList]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [...defaultCompatible, ValueAggregation.ValueListCountAggregator],
  },
  [UtrValueType.Text]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [
      ...defaultCompatible,
      ValueAggregation.TextCountAggregator,
      ValueAggregation.ValueConcatenateAggregator,
    ],
  },
  [UtrValueType.Date]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [...defaultCompatible, ValueAggregation.TextCountAggregator],
  },

  // Unhandled
  [UtrValueType.Sample]: defaultLatestCompatibility,
  [UtrValueType.TextValueList]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [...defaultCompatible, ValueAggregation.TextCountAggregator],
  },
};

export const ValueAggregationChildrenCompatibility: ValueAggregationCompatibilityInterface = {
  [UtrValueType.Number]: {
    default: ValueAggregation.ValueSumAggregator,
    compatible: [ValueAggregation.ValueSumAggregator, ValueAggregation.EmptyAggregator],
  },
  [UtrValueType.Percentage]: {
    default: ValueAggregation.ValueAverageAggregator,
    compatible: [ValueAggregation.ValueAverageAggregator, ValueAggregation.EmptyAggregator],
  },
  [UtrValueType.Table]: {
    default: ValueAggregation.TableColumnAggregator,
    compatible: [
      ValueAggregation.TableColumnAggregator,
      ValueAggregation.TableRowGroupAggregator,
      ValueAggregation.EmptyAggregator,
    ],
  },
  [UtrValueType.NumericValueList]: {
    default: ValueAggregation.NumericValueListSumAggregator,
    compatible: [ValueAggregation.NumericValueListSumAggregator, ValueAggregation.EmptyAggregator],
  },
  [UtrValueType.ValueListMulti]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [ValueAggregation.LatestAggregator, ValueAggregation.EmptyAggregator],
  },
  [UtrValueType.ValueList]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [ValueAggregation.LatestAggregator, ValueAggregation.EmptyAggregator],
  },
  [UtrValueType.Text]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [
      ValueAggregation.LatestAggregator,
      ValueAggregation.EmptyAggregator,
      ValueAggregation.ValueConcatenateAggregator,
    ],
  },
  [UtrValueType.Date]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [ValueAggregation.LatestAggregator, ValueAggregation.EmptyAggregator],
  },

  // Unhandled
  [UtrValueType.Sample]: defaultLatestCompatibility,
  [UtrValueType.TextValueList]: defaultLatestCompatibility,
};

export const ColumnAggregationCompatibility: ColumnAggregationCompatibilityInterface = {
  [TableColumnType.Number]: {
    default: ColumnValueAggregation.ColumnSumAggregator,
    compatible: [
      ColumnValueAggregation.ColumnAverageAggregator,
      ColumnValueAggregation.ColumnLatestAggregator,
      ColumnValueAggregation.ColumnMaxAggregator,
      ColumnValueAggregation.ColumnEmptyAggregator,
      ColumnValueAggregation.ColumnPostAggregationCalculation,
      ColumnValueAggregation.ColumnWeightedAverageAggregator,
    ],
  },
  [TableColumnType.Percentage]: {
    default: ColumnValueAggregation.ColumnAverageAggregator,
    compatible: [
      ColumnValueAggregation.ColumnSumAggregator,
      ColumnValueAggregation.ColumnLatestAggregator,
      ColumnValueAggregation.ColumnMaxAggregator,
      ColumnValueAggregation.ColumnEmptyAggregator,
      ColumnValueAggregation.ColumnPostAggregationCalculation,
      ColumnValueAggregation.ColumnWeightedAverageAggregator,
    ],
  },
  [TableColumnType.Text]: {
    default: ColumnValueAggregation.ColumnLatestAggregator,
    compatible: [ColumnValueAggregation.ColumnEmptyAggregator, ColumnValueAggregation.ColumnConcatenateAggregator],
  },
  [TableColumnType.Date]: {
    default: ColumnValueAggregation.ColumnLatestAggregator,
    compatible: [ColumnValueAggregation.ColumnEmptyAggregator],
  },
  [TableColumnType.ValueListMulti]: {
    default: ColumnValueAggregation.ColumnLatestAggregator,
    compatible: [ColumnValueAggregation.ColumnEmptyAggregator],
  },
  [TableColumnType.ValueList]: {
    default: ColumnValueAggregation.ColumnLatestAggregator,
    compatible: [ColumnValueAggregation.ColumnEmptyAggregator],
  },
};
