import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { Label } from 'reactstrap';
import {
  AGGREGATION_CONFIG_CODE,
  OVERRIDES_CONFIG_CODES,
  UTRV_CONFIG_CODES,
  UtrvConfigCode,
  UtrvConfigType,
  UtrvConfigValue,
} from './constants';
import { getLabelByCode, getOptionsByCode, getAggregationOptionsByValueType } from './utils';
import { UtrValueType, AggregationMode } from '@g17eco/types/universalTracker';
import { OverrideAggregationConfig } from '@g17eco/types/initiativeUniversalTracker';
import { AggregationConfigDropdown } from './AggregationConfigDropdown';
import { BulkActionUtr } from '@g17eco/types/survey-question-list';
import { useAppSelector } from '@reducers/index';
import { isStaff } from '@selectors/user';

interface Props {
  utrvConfig: UtrvConfigType;
  aggregationConfig?: OverrideAggregationConfig;
  selectedQuestions: BulkActionUtr[];
  handleChange: (props: { code: UtrvConfigCode; value: UtrvConfigValue }) => void;
  handleAggregationConfigChange: (aggregationConfig: OverrideAggregationConfig) => void;
}

export const SingleMetricOverride = (props: Props) => {
  const { utrvConfig, aggregationConfig, selectedQuestions, handleChange, handleAggregationConfigChange } = props;

  const isStaffUser = useAppSelector(isStaff);

  // Generate options based on the first question's valueType
  const valueType = selectedQuestions[0]?.valueType;
  const childrenOptions = valueType ? getAggregationOptionsByValueType(valueType as UtrValueType, AggregationMode.Children) : [];
  const combinedOptions = valueType ? getAggregationOptionsByValueType(valueType as UtrValueType, AggregationMode.Combined) : [];
  const CONFIG_CODES = isStaffUser ? OVERRIDES_CONFIG_CODES : UTRV_CONFIG_CODES;

  return (
    <>
      {CONFIG_CODES.map((code, index) => {
        if (code === AGGREGATION_CONFIG_CODE) {
          return (
            <div key={code} className={index ? 'mt-3' : ''}>
              <Label style={{ fontWeight: 500 }}>{getLabelByCode(code)}</Label>
              <AggregationConfigDropdown
                childrenOptions={childrenOptions}
                combinedOptions={combinedOptions}
                aggregationConfig={aggregationConfig}
                onChange={handleAggregationConfigChange}
              />
            </div>
          );
        }

        const options = getOptionsByCode(code);
        return (
          <div key={code}>
            <Label for={code} className={index ? 'mt-3' : ''} style={{ fontWeight: 500 }}>
              {getLabelByCode(code)}
            </Label>
            <SelectFactory
              selectType={SelectTypes.SingleSelect}
              options={options}
              value={options.find((op) => op.value === utrvConfig[code]) ?? null}
              onChange={(op) => (op?.value ? handleChange({ code, value: op.value }) : undefined)}
              isMenuPortalTargetBody
              menuPlacement='top'
            />
          </div>
        );
      })}
    </>
  );
};
