import React from 'react';
import { SelectFactory, SelectTypes, Option } from '@g17eco/molecules/select/SelectFactory';
import { Label } from 'reactstrap';
import { AggregationMode, ValueAggregation } from '@g17eco/types/universalTracker';
import { OverrideAggregationConfig } from '@g17eco/types/initiativeUniversalTracker';

interface Props {
  childrenOptions: Option<ValueAggregation | 'default'>[];
  combinedOptions: Option<ValueAggregation | 'default'>[];
  aggregationConfig?: OverrideAggregationConfig;
  onChange: (aggregationConfig: OverrideAggregationConfig) => void;
  className?: string;
  isDisabled?: boolean;
}

export const AggregationConfigDropdown = ({
  childrenOptions,
  combinedOptions,
  aggregationConfig,
  onChange,
  className,
  isDisabled = false,
}: Props) => {
  // Current values
  const childrenValue = aggregationConfig?.modes?.children?.valueAggregation;
  const combinedValue = aggregationConfig?.modes?.combined?.valueAggregation;

  const handleChange = (option: Option<ValueAggregation | 'default'> | null, mode: AggregationMode) => {
    const newSettings: OverrideAggregationConfig = {
      ...aggregationConfig,
      modes: {
        ...aggregationConfig?.modes,
        [mode]: {
          ...aggregationConfig?.modes?.[mode],
          valueAggregation: option?.value,
        }
      },
    };
    onChange(newSettings);
  };

  return (
    <div className={className}>
      <div className='mb-2'>
        <Label>Combined Aggregation</Label>
        <SelectFactory
          selectType={SelectTypes.SingleSelect}
          options={combinedOptions}
          value={combinedOptions.find((op) => op.value === combinedValue) ?? null}
          onChange={(option) => handleChange(option, AggregationMode.Combined)}
          isMenuPortalTargetBody
          menuPlacement='top'
          isDisabled={isDisabled}
          isClearable
          placeholder='Select aggregation type...'
        />
      </div>
      <div>
        <Label>Children Aggregation</Label>
        <SelectFactory
          selectType={SelectTypes.SingleSelect}
          options={childrenOptions}
          value={childrenOptions.find((op) => op.value === childrenValue) ?? null}
          onChange={(option) => handleChange(option, AggregationMode.Children)}
          isMenuPortalTargetBody
          menuPlacement='top'
          isDisabled={isDisabled}
          isClearable
          placeholder='Select aggregation type...'
        />
      </div>
    </div>
  );
};
