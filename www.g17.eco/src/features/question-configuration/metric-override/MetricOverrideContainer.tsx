import {
  useGetInitiativeUniversalTrackersQuery,
  useOverrideUtrvConfigMutation,
  useOverrideAggregationConfigMutation,
} from '@api/initiative-universal-trackers';
import { addSiteError } from '@g17eco/slices/siteAlertsSlice';
import { InitiativeUniversalTracker, OverrideAggregationConfig } from '@g17eco/types/initiativeUniversalTracker';
import { UtrvConfigType } from './constants';
import { MetricOverrideForm } from './MetricOverrideForm';
import { getInitialAggregationConfig, getInitialUtrvConfig } from './utils';
import { MetricOverrideProps } from '../types';
import { useMemo } from 'react';
import { useAppSelector } from '@reducers/index';
import { isStaff } from '@selectors/user';

export const MetricOverrideContainer = (props: MetricOverrideProps) => {
  const { handleReload, selectedQuestions, initiativeId, handleCloseModal } = props;
  const selectedUtrIds = selectedQuestions.map((q) => q._id);
  
  const isStaffUser = useAppSelector(isStaff);

  const { data: initiativeUtrs, isFetching } = useGetInitiativeUniversalTrackersQuery(initiativeId, {
    skip: !initiativeId || selectedQuestions.length === 0,
  });

  const initiativeUtrMap = useMemo(() => {
    return new Map<string, InitiativeUniversalTracker>(
      (initiativeUtrs ?? []).map((utr: InitiativeUniversalTracker) => [utr.universalTrackerId, utr])
    );
  }, [initiativeUtrs]);

  const [overrideUtrvConfig, { isLoading: isUpdating }] = useOverrideUtrvConfigMutation();
  const [overrideAggregationConfig, { isLoading: isUpdatingAggregation }] = useOverrideAggregationConfigMutation();

  const initialUtrvConfig = getInitialUtrvConfig({ initiativeUtrMap, selectedQuestions });
  const initialAggregationConfig = getInitialAggregationConfig({ initiativeUtrMap, utrId: selectedUtrIds[0] });

  const isLoading = isFetching || isUpdating || isUpdatingAggregation;

  const onClickUpdate = (utrvConfig: Partial<UtrvConfigType>) => {
    overrideUtrvConfig({ initiativeId, utrvConfig, utrIds: selectedUtrIds })
      .unwrap()
      .then(() => {
        handleReload({ reloadSurvey: true, closeModal: selectedQuestions.length === 1 });
      })
      .catch((err) => {
        addSiteError(err);
        handleReload();
      });
  };

  const handleAggregationConfigUpdate = (aggregationConfig: OverrideAggregationConfig) => {
    // Prevent non-staff and multiple metrics update
    if (!isStaffUser || selectedUtrIds.length !== 1) {
      return;
    }
    
    const utrId = selectedUtrIds[0];
    overrideAggregationConfig({ initiativeId, aggregationConfig, utrId })
      .unwrap()
      .then(() => {
        handleReload({ reloadSurvey: true, closeModal: true });
      })
      .catch((err) => {
        addSiteError(err);
        handleReload();
      });
  };

  return (
    <MetricOverrideForm
      key={JSON.stringify({ initialUtrvConfig, initialAggregationConfig })}
      isLoading={isLoading}
      handleUpdate={onClickUpdate}
      handleCloseModal={handleCloseModal}
      initialUtrvConfig={initialUtrvConfig}
      isMultipleUpdate={selectedUtrIds.length > 1}
      selectedQuestions={selectedQuestions}
      initialAggregationConfig={initialAggregationConfig}
      handleAggregationConfigUpdate={handleAggregationConfigUpdate}
    />
  );
};
